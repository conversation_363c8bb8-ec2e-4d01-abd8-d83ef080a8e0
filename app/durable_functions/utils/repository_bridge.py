"""
Repository bridge for durable functions to access main application repositories
without triggering main FastAPI settings loading.

This module provides factory functions that create repository instances using
the durable functions configuration instead of the main application configuration.
"""

import logging
from typing import TYPE_CHECKING

from sqlalchemy.ext.asyncio import AsyncSession

from durable_functions.application.config import settings as df_settings
from durable_functions.application.db import async_session_local


if TYPE_CHECKING:
    from repositories import (
        ConversationMessageRepository,
        ConversationRepository,
        DocumentQueueRepository,
        ExtractedDataRepository,
        ProcessingMessageRepository,
    )

logger = logging.getLogger(__name__)


def get_conversation_repository(session: AsyncSession) -> 'ConversationRepository':
    """Get ConversationRepository instance for durable functions."""
    # Import here to avoid triggering main config loading
    from repositories.conversation import ConversationRepository
    return ConversationRepository(session)


def get_conversation_message_repository(
    session: AsyncSession, 
    conversation_repository: 'ConversationRepository'
) -> 'ConversationMessageRepository':
    """Get ConversationMessageRepository instance for durable functions."""
    # Import here to avoid triggering main config loading
    from repositories.conversation_message import ConversationMessageRepository
    return ConversationMessageRepository(
        db_session=session, 
        conversation_repository=conversation_repository
    )


def get_extracted_data_repository(
    session: AsyncSession, 
    conversation_repository: 'ConversationRepository'
) -> 'ExtractedDataRepository':
    """Get ExtractedDataRepository instance for durable functions."""
    # Import here to avoid triggering main config loading
    from repositories.extracted_data import ExtractedDataRepository
    return ExtractedDataRepository(
        db_session=session, 
        conversation_repository=conversation_repository
    )


def get_processing_message_repository(session: AsyncSession) -> 'ProcessingMessageRepository':
    """Get ProcessingMessageRepository instance for durable functions."""
    # Import here to avoid triggering main config loading
    from repositories.processing_message import ProcessingMessageRepository
    return ProcessingMessageRepository(session)


def get_document_queue_repository(
    connection_string: str | None = None,
    queue_name: str | None = None
) -> 'DocumentQueueRepository':
    """Get DocumentQueueRepository instance for durable functions."""
    # We need to create a custom DocumentQueueRepository that doesn't import main config
    # Import here to avoid triggering main config loading
    from durable_functions.repositories.document_queue import DocumentQueueRepository

    # Use durable functions settings if not provided
    conn_str = connection_string or df_settings.QUEUE_SETTINGS.CONNECTION_STRING
    q_name = queue_name or df_settings.QUEUE_SETTINGS.DOCUMENT_PROCESSING_QUEUE

    return DocumentQueueRepository(conn_str, q_name) # type: ignore


# Convenience functions for common patterns
async def create_conversation_repositories(session: AsyncSession) -> tuple[
    'ConversationMessageRepository', 
    'ExtractedDataRepository'
]:
    """Create commonly used conversation-related repositories."""
    conversation_repo = get_conversation_repository(session)
    message_repo = get_conversation_message_repository(session, conversation_repo)
    extracted_data_repo = get_extracted_data_repository(session, conversation_repo)
    
    return message_repo, extracted_data_repo


async def get_db_session() -> AsyncSession:
    """Get database session for durable functions."""
    return async_session_local()  # type: ignore
